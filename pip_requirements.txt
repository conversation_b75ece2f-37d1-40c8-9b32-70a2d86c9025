accelerate==1.7.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
alembic==1.16.2
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
attrs==25.3.0
av==14.4.0
blend_modes==2.2.0
BlurGenerator==1.1.0
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.1.8
color-matcher==0.6.0
comfyui-embedded-docs==0.2.0
comfyui_frontend_package==1.21.7
comfyui_workflow_templates==0.1.25
contourpy==1.3.2
cryptography==45.0.2
cycler==0.12.1
ddt==1.7.2
debugpy==1.8.13
deepdiff==8.5.0
Deprecated==1.2.18
diffusers==0.32.2
dill==0.4.0
docutils==0.21.2
einops==0.8.1
ffmpeg-python==0.2.0
filelock==3.18.0
flash_attn==2.0.2
fonttools==4.58.0
frozenlist==1.6.0
fsspec==2025.5.0
ftfy==6.3.1
future==1.0.0
git-lfs==1.6
gitdb==4.0.12
GitPython==3.1.44
gradio_client==1.10.1
greenlet==3.2.3
h11==0.16.0
hf-xet==1.1.2
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.25.2
hydra-core==1.3.2
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
importlib_metadata==8.7.0
iopath==0.1.10
Jinja2==3.1.6
kiwisolver==1.4.8
kornia==0.8.1
kornia_rs==0.1.9
lazy_loader==0.4
lightning-utilities==0.14.3
llvmlite==0.44.0
loguru==0.7.3
ltx-video @ git+https://github.com/Lightricks/LTX-Video@ea6d5d64894d617ec16b7d70a44e85b8499b3225
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.3
matrix-client==0.4.0
mdurl==0.1.2
mpmath==1.3.0
mss==10.0.0
multidict==6.4.3
networkx==3.4.2
ninja==********
numba==0.61.2
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-ml-py==12.575.51
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
omegaconf==2.3.0
open_clip_torch==2.32.0
opencv-python==*********
opencv-python-headless==*********
orderly-set==5.4.1
packaging==25.0
piexif==1.1.3
pillow==11.2.1
portalocker==3.2.0
propcache==0.3.1
psutil==7.0.0
py-cpuinfo==9.0.0
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.10.1
pydantic_core==2.33.2
PyGithub==2.6.1
Pygments==2.19.1
PyJWT==2.10.1
PyNaCl==1.5.0
pynvml==12.0.0
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytorch-lightning==2.5.1.post0
PyYAML==6.0.2
qwen-vl-utils==0.0.11
regex==2024.11.6
requests==2.32.3
requirements-parser==0.11.0
rich==14.0.0
safetensors==0.5.3
sageattention @ git+https://github.com/thu-ml/SageAttention.git@b554fc49b59fbcbbcbceae6ccc8bc4d7ea60950d
scikit-image==0.25.2
scipy==1.15.3
segment-anything==1.0
segment-anything-py==1.0.1
sentencepiece==0.2.0
setuptools==78.1.1
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soundfile==0.13.1
spandrel==0.4.1
SQLAlchemy==2.0.41
sympy==1.14.0
tifffile==2025.5.10
timm==1.0.15
tokenizers==0.21.1
toml==0.10.2
torch==2.7.1
torchaudio==2.7.0+cu128
torchmetrics==1.7.1
torchsde==0.2.6
torchvision==0.22.0+cu128
tqdm==4.67.1
trampoline==0.1.2
transformers==4.48.3
triton==3.3.1
typer==0.15.4
types-setuptools==80.7.0.20250516
typing-inspection==0.4.0
typing_extensions==4.13.2
urllib3==1.26.20
uv==0.7.5
wcwidth==0.2.13
websockets==15.0.1
wheel==0.45.1
wrapt==1.17.2
xformers==0.0.31
yarl==1.20.0
zipp==3.21.0
