name: z
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - expat=2.7.1=h6a678d5_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.16=h5eee18b_0
  - pip=25.1=pyhc872135_2
  - python=3.12.0=h996f2a0_0
  - readline=8.2=h5eee18b_0
  - setuptools=78.1.1=py312h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - tzdata=2025b=h04d1e81_0
  - wheel=0.45.1=py312h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - accelerate==1.7.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.18
      - aiosignal==1.3.2
      - alembic==1.16.2
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.9.0
      - attrs==25.3.0
      - av==14.4.0
      - blend-modes==2.2.0
      - blurgenerator==1.1.0
      - certifi==2025.4.26
      - cffi==1.17.1
      - chardet==5.2.0
      - charset-normalizer==3.4.2
      - click==8.1.8
      - color-matcher==0.6.0
      - comfyui-embedded-docs==0.2.0
      - comfyui-frontend-package==1.21.7
      - comfyui-workflow-templates==0.1.25
      - contourpy==1.3.2
      - cryptography==45.0.2
      - cycler==0.12.1
      - ddt==1.7.2
      - deprecated==1.2.18
      - diffusers==0.32.2
      - dill==0.4.0
      - docutils==0.21.2
      - einops==0.8.1
      - ffmpeg-python==0.2.0
      - filelock==3.18.0
      - flash-attn==2.0.2
      - fonttools==4.58.0
      - frozenlist==1.6.0
      - fsspec==2025.3.2
      - ftfy==6.3.1
      - future==1.0.0
      - gitdb==4.0.12
      - gitpython==3.1.44
      - gradio-client==1.10.1
      - greenlet==3.2.3
      - h11==0.16.0
      - httpcore==1.0.9
      - httpx==0.28.1
      - huggingface-hub==0.25.2
      - hydra-core==1.3.2
      - idna==3.10
      - imageio==2.37.0
      - imageio-ffmpeg==0.6.0
      - importlib-metadata==8.7.0
      - iopath==0.1.10
      - jinja2==3.1.6
      - kiwisolver==1.4.8
      - kornia==0.8.1
      - kornia-rs==0.1.9
      - lazy-loader==0.4
      - lightning-utilities==0.14.3
      - llvmlite==0.44.0
      - loguru==0.7.3
      - ltx-video==0.1.2
      - mako==1.3.10
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - matplotlib==3.10.3
      - matrix-client==0.4.0
      - mdurl==0.1.2
      - mpmath==1.3.0
      - mss==10.0.0
      - multidict==6.4.3
      - networkx==3.4.2
      - ninja==1.11.1.4
      - numba==0.61.2
      - numpy==1.26.4
      - nvidia-cublas-cu12==12.6.4.1
      - nvidia-cuda-cupti-cu12==12.6.80
      - nvidia-cuda-nvrtc-cu12==12.6.77
      - nvidia-cuda-runtime-cu12==12.6.77
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-cufile-cu12==********
      - nvidia-curand-cu12==*********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==********
      - nvidia-cusparselt-cu12==0.6.3
      - nvidia-ml-py==12.575.51
      - nvidia-nccl-cu12==2.26.2
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.6.77
      - omegaconf==2.3.0
      - open-clip-torch==2.32.0
      - opencv-python==*********
      - opencv-python-headless==*********
      - packaging==25.0
      - piexif==1.1.3
      - pillow==11.2.1
      - portalocker==3.2.0
      - propcache==0.3.1
      - psutil==7.0.0
      - py-cpuinfo==9.0.0
      - pycparser==2.22
      - pydantic==2.11.4
      - pydantic-core==2.33.2
      - pydantic-settings==2.10.1
      - pygithub==2.6.1
      - pygments==2.19.1
      - pyjwt==2.10.1
      - pynacl==1.5.0
      - pynvml==12.0.0
      - pyparsing==3.2.3
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.1.1
      - pytorch-lightning==2.5.1.post0
      - pyyaml==6.0.2
      - qwen-vl-utils==0.0.11
      - regex==2024.11.6
      - requests==2.32.3
      - requirements-parser==0.11.0
      - rich==14.0.0
      - safetensors==0.5.3
      - sageattention==2.1.1
      - scikit-image==0.25.2
      - scipy==1.15.3
      - segment-anything==1.0
      - segment-anything-py==1.0.1
      - sentencepiece==0.2.0
      - shellingham==1.5.4
      - six==1.17.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - soundfile==0.13.1
      - spandrel==0.4.1
      - sqlalchemy==2.0.41
      - sympy==1.14.0
      - tifffile==2025.5.10
      - timm==1.0.15
      - tokenizers==0.21.1
      - toml==0.10.2
      - torch==2.7.1
      - torchaudio==2.7.0+cu128
      - torchmetrics==1.7.1
      - torchsde==0.2.6
      - torchvision==0.22.0+cu128
      - tqdm==4.67.1
      - trampoline==0.1.2
      - transformers==4.48.3
      - triton==3.3.1
      - typer==0.15.4
      - types-setuptools==80.7.0.20250516
      - typing-extensions==4.13.2
      - typing-inspection==0.4.0
      - urllib3==1.26.20
      - uv==0.7.5
      - wcwidth==0.2.13
      - websockets==15.0.1
      - wrapt==1.17.2
      - xformers==0.0.31
      - yarl==1.20.0
      - zipp==3.21.0
prefix: /home/<USER>/.conda/envs/z
