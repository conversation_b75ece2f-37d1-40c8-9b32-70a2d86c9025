#!/bin/bash
#default disabled_nodes = disabled_nodes.txt if $1 assign to disabled_nodes
#default PORT = 18892, if $2 assign to port 
echo "Usage start.sh [port=18897] [run_type=[SSL],DEBUG, AD] [comfy_dir=script_dir] [conda_env=z]"

if [ -z "$1" ]
then
    PORT=18897
else
    PORT=$1
fi
if [ -z "$2" ]
then
    run_type=SSL
elif [ "$2" = "DEBUG" ]; then
    run_type=DEBUG
elif [ "$2" = "AD" ]; then
    run_type=AD
else
    run_type=SSL
fi

if [ -z "$3" ]
then
    #use the script dir
    base_dir=$(dirname "$0")
    comfy_dir=$base_dir
else
    comfy_dir=$3
fi
if [ -z "$4" ]
then
    conda_env=z
else
    conda_env=$4
fi

echo "++ starting comfyui in comfy_dir ${comfy_dir} with port ${PORT}, run_type ${run_type} +++ ${conda_env}"

existing=$(ps -aef | grep python | grep main.py | grep $PORT)
echo ${existing}
if [[ -z "${existing}" ]]; then
    # Check if comfy_dir exists; if not, exit with an error
    if [ -d ${comfy_dir} ]; then
        cd ${comfy_dir}
    else
        echo "++++ directory ${comfy_dir} does not exist ++++"
        exit 1
    fi

    attempt=1
    max_attempts=6
    eval "$(conda shell.bash hook)" && conda deactivate && unset PYTHONPATH && conda activate $conda_env
    conda env list
    
    sleep 2
    while [ $attempt -le $max_attempts ]; do
        echo "Attempt $attempt to start process"
        echo "echo $CONDA_DEFAULT_ENV"

        # Determine mode and start the process
        if [ $run_type == "SSL" ]; then
            echo "regular mode"
            nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --tls-keyfile /etc/letsencrypt/qstools/certs/privkey.pem \
               --tls-certfile /etc/letsencrypt/qstools/certs/cert.pem \
               --use-pytorch-cross-attention --port $PORT 2>&1 >/tmp/comfy_$PORT.out &
        elif [ $run_type == "DEBUG" ]; then
            echo "debug mode"
            nohup python main.py -enable-cors-header --multi-user --verbose --listen 0.0.0.0 \
               --disable-all-custom-nodes \
               --tls-keyfile /etc/letsencrypt/qstools/certs/privkey.pem \
               --tls-certfile /etc/letsencrypt/qstools/certs/cert.pem \
               --use-pytorch-cross-attention --port $PORT 2>&1 >/tmp/comfy_$PORT.out &
        elif [ $run_type == "AD" ]; then
            echo "AD mode"
            nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port $PORT 2>&1 >/tmp/comfy_$PORT.out &
        else
            echo "unknown mode"
        fi

        comfyuipid=$!
        echo "Started with pid ${comfyuipid}"
        disown
        echo ${comfyuipid} > /tmp/comfy_${PORT}.pid

        # Wait for a moment and check if the process is still running
        sleep 15
        if ps -p $comfyuipid > /dev/null; then
            echo "Process started successfully"
            echo "Logs are here: /tmp/comfy_$PORT.out"
            exit 0
        else
            echo "Failed to start process. Retrying in 15 seconds..."
            attempt=$((attempt + 1))
        fi
    done

    echo "Failed to start the process after $max_attempts attempts"
    exit 1
else
    echo "Process already running. Stop it first."
    exit 1
fi
